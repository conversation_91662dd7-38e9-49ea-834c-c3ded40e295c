<?php
// =================================================================
// Endpoint: Guardar Prospecto InteletGroup
// =================================================================

// -----------------------------------------------------------------
// 1. CONFIGURACIÓN Y BOOTSTRAP
// -----------------------------------------------------------------
ini_set('display_errors', 0); // No mostrar errores en producción
error_reporting(E_ALL);

// Registrar un manejador de errores centralizado
set_error_handler(function($severity, $message, $file, $line) {
    error_log("Error: [$severity] $message in $file on line $line");
});

set_exception_handler(function($exception) {
    error_log("Uncaught exception: " . $exception->getMessage());
    responder_json([
        'success' => false,
        'message' => 'Error interno del servidor. Por favor, contacte a soporte.'
    ], 500);
});

// Headers de respuesta
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, no-store, must-revalidate');

// Dependencias
require_once 'con_db.php'; // Asume que $mysqli está aquí

// -----------------------------------------------------------------
// 2. FUNCIÓN DE RESPUESTA CENTRALIZADA
// -----------------------------------------------------------------
/**
 * Envía una respuesta JSON estandarizada y termina el script.
 *
 * @param array $data Los datos a codificar en JSON.
 * @param int $http_code El código de estado HTTP.
 */
function responder_json($data, $http_code = 200) {
    http_response_code($http_code);
    echo json_encode($data);
    exit;
}

// -----------------------------------------------------------------
// 3. LÓGICA PRINCIPAL DEL ENDPOINT
// -----------------------------------------------------------------
function manejar_solicitud($db_connection) {
    // Verificar método de solicitud
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        responder_json(['success' => false, 'message' => 'Método no permitido'], 405);
    }

    // Iniciar sesión y verificar autenticación
    session_start();
    if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
        responder_json(['success' => false, 'message' => 'No autorizado'], 401);
    }

    // Obtener y sanitizar datos del formulario
    $datos_prospecto = obtener_datos_formulario();

    // Validar los datos
    $errores_validacion = validar_datos($datos_prospecto);
    if (!empty($errores_validacion)) {
        responder_json([
            'success' => false,
            'message' => 'Datos inválidos',
            'errors' => $errores_validacion
        ], 400);
        return; // Asegurar que el script termine aquí
    }

    // Verificar si el RUT ya existe
    if (rut_existe($db_connection, $datos_prospecto['rut_cliente'])) {
        responder_json([
            'success' => false,
            'message' => 'Ya existe un prospecto con este RUT.',
            'errors' => ['rut_cliente' => 'Este RUT ya está registrado.']
        ], 409); // 409 Conflict
        return; // Asegurar que el script termine aquí
    }

    // Iniciar transacción
    $db_connection->begin_transaction();

    try {
        // Insertar prospecto en la base de datos
        $prospecto_id = insertar_prospecto($db_connection, $datos_prospecto, $_SESSION['usuario_id']);

        // Registrar en bitácora
        registrar_bitacora($db_connection, $prospecto_id, $_SESSION['usuario_id'], $datos_prospecto);

        // Procesar archivos adjuntos si existen
        $archivos_procesados = procesar_archivos_checklist($db_connection, $prospecto_id, $_SESSION['usuario_id'], $datos_prospecto);

        // Confirmar transacción
        $db_connection->commit();

        // Enviar notificación (se puede hacer de forma asíncrona en un sistema más complejo)
        enviar_notificacion($datos_prospecto);

        // Respuesta exitosa
        $mensaje = 'Prospecto registrado exitosamente.';
        if ($archivos_procesados > 0) {
            $mensaje .= " Se procesaron $archivos_procesados archivo(s) adjunto(s).";
        }

        responder_json([
            'success' => true,
            'message' => $mensaje,
            'prospecto_id' => $prospecto_id,
            'archivos_procesados' => $archivos_procesados
        ]);

    } catch (Exception $e) {
        // Revertir transacción en caso de error
        $db_connection->rollback();
        error_log("Error en transacción de prospecto: " . $e->getMessage());
        responder_json(['success' => false, 'message' => 'No se pudo guardar el prospecto en la base de datos.'], 500);
    }
}

// -----------------------------------------------------------------
// 4. FUNCIONES AUXILIARES
// -----------------------------------------------------------------

/**
 * Obtiene y limpia los datos del POST.
 * @return array
 */
function obtener_datos_formulario() {
    $campos = [
        'nombre_ejecutivo', 'rut_cliente', 'razon_social', 'rubro', 
        'direccion_comercial', 'telefono_celular', 'email', 'numero_pos', 
        'tipo_cuenta', 'numero_cuenta_bancaria', 'dias_atencion', 
        'horario_atencion', 'contrata_boleta', 'competencia_actual', 'tipo_persona'
    ];
    
    $datos = [];
    foreach ($campos as $campo) {
        $datos[$campo] = trim($_POST[$campo] ?? '');
    }
    
    // Limpieza específica
    $datos['razon_social'] = strtoupper($datos['razon_social']);
    
    return $datos;
}

/**
 * Valida los datos del prospecto.
 * @param array $datos
 * @return array Lista de errores.
 */
function validar_datos($datos) {
    $errores = [];
    
    if (empty($datos['rut_cliente'])) $errores['rut_cliente'] = 'El RUT es requerido.';
    elseif (!preg_match('/^\d{7,8}-[\dkK]$/', $datos['rut_cliente'])) $errores['rut_cliente'] = 'Formato de RUT inválido.';

    if (empty($datos['razon_social'])) $errores['razon_social'] = 'La razón social es requerida.';
    elseif (!preg_match('/^[A-Z0-9\s\.\-\&]+$/', $datos['razon_social'])) $errores['razon_social'] = 'La razón social solo debe contener letras mayúsculas, números, espacios y caracteres: . - &';

    if (empty($datos['email'])) $errores['email'] = 'El email es requerido.';
    elseif (!filter_var($datos['email'], FILTER_VALIDATE_EMAIL)) $errores['email'] = 'Formato de email inválido.';

    // Añadir aquí el resto de validaciones requeridas...
    if (empty($datos['rubro'])) $errores['rubro'] = 'El rubro es requerido.';
    if (empty($datos['telefono_celular'])) $errores['telefono_celular'] = 'El teléfono es requerido.';
    if (empty($datos['tipo_persona'])) $errores['tipo_persona'] = 'El tipo de persona es requerido.';
    elseif (!in_array($datos['tipo_persona'], ['Natural', 'Juridica'])) $errores['tipo_persona'] = 'Tipo de persona inválido.';
    
    return $errores;
}

/**
 * Verifica si un RUT ya existe en la base de datos.
 * @param mysqli $db
 * @param string $rut
 * @return bool
 */
function rut_existe($db, $rut) {
    $sql = "SELECT id FROM tb_inteletgroup_prospectos WHERE rut_cliente = ? LIMIT 1";
    $stmt = $db->prepare($sql);
    $stmt->bind_param("s", $rut);
    $stmt->execute();
    $stmt->store_result();
    $existe = $stmt->num_rows > 0;
    $stmt->close();
    return $existe;
}


/**
 * Inserta el nuevo prospecto en la base de datos.
 * @param mysqli $db
 * @param array $datos
 * @param int $usuario_id
 * @return int ID del prospecto insertado.
 */
function insertar_prospecto($db, $datos, $usuario_id) {
    $sql = "INSERT INTO tb_inteletgroup_prospectos (
                usuario_id, nombre_ejecutivo, rut_cliente, razon_social, rubro,
                direccion_comercial, telefono_celular, email, numero_pos, tipo_cuenta,
                numero_cuenta_bancaria, dias_atencion, horario_atencion, contrata_boleta,
                competencia_actual, tipo_persona
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $db->prepare($sql);
    $stmt->bind_param("isssssssssssssss",
        $usuario_id, $datos['nombre_ejecutivo'], $datos['rut_cliente'], $datos['razon_social'], $datos['rubro'],
        $datos['direccion_comercial'], $datos['telefono_celular'], $datos['email'], $datos['numero_pos'], $datos['tipo_cuenta'],
        $datos['numero_cuenta_bancaria'], $datos['dias_atencion'], $datos['horario_atencion'], $datos['contrata_boleta'],
        $datos['competencia_actual'], $datos['tipo_persona']
    );

    if (!$stmt->execute()) {
        throw new Exception("Error al ejecutar la inserción: " . $stmt->error);
    }

    $prospecto_id = $db->insert_id;
    $stmt->close();

    if ($prospecto_id == 0) {
        throw new Exception("La inserción no generó un ID de prospecto.");
    }

    return $prospecto_id;
}

/**
 * Registra la acción en la bitácora.
 * @param mysqli $db
 * @param int $prospecto_id
 * @param int $usuario_id
 * @param array $datos
 */
function registrar_bitacora($db, $prospecto_id, $usuario_id, $datos) {
    $sql = "INSERT INTO tb_inteletgroup_prospecto_bitacora (prospecto_id, usuario_id, accion, descripcion) VALUES (?, ?, 'Crear', ?)";
    $descripcion = "Prospecto creado: {$datos['razon_social']} (RUT: {$datos['rut_cliente']})";
    
    $stmt = $db->prepare($sql);
    $stmt->bind_param("iis", $prospecto_id, $usuario_id, $descripcion);
    
    if (!$stmt->execute()) {
        // No lanzamos una excepción aquí para no revertir la transacción principal,
        // pero sí lo registramos como un problema.
        error_log("ALERTA: No se pudo registrar en la bitácora para el prospecto ID: $prospecto_id");
    }
    $stmt->close();
}

/**
 * Envía una notificación por correo.
 * @param array $datos
 */
function enviar_notificacion($datos) {
    $to = '<EMAIL>';
    $subject = "Nuevo Prospecto Registrado - {$datos['razon_social']} (RUT: {$datos['rut_cliente']})";
    $body = "Se ha registrado un nuevo prospecto con los siguientes datos:<br><br>" .
            "<ul>" .
            "<li><strong>Ejecutivo:</strong> {$datos['nombre_ejecutivo']}</li>" .
            "<li><strong>RUT:</strong> {$datos['rut_cliente']}</li>" .
            "<li><strong>Razón Social:</strong> {$datos['razon_social']}</li>" .
            "<li><strong>Email:</strong> {$datos['email']}</li>" .
            "</ul>";
    $headers = "MIME-Version: 1.0\r\n" .
               "Content-type:text/html;charset=UTF-8\r\n" .
               "From: Sistema Intranet <<EMAIL>>\r\n";
    
    // La función mail() puede ser lenta. En un sistema real, esto debería
    // delegarse a un servicio de cola de correos.
    // @mail($to, $subject, $body, $headers);
    error_log("Notificación por correo preparada para: {$datos['rut_cliente']}");
}

/**
 * Procesa los archivos del checklist de documentos
 * @param mysqli $db Conexión a la base de datos
 * @param int $prospecto_id ID del prospecto
 * @param int $usuario_id ID del usuario
 * @param array $datos_prospecto Datos del prospecto incluyendo RUT y tipo de persona
 * @return int Número de archivos procesados exitosamente
 */
function procesar_archivos_checklist($db, $prospecto_id, $usuario_id, $datos_prospecto) {
    $archivos_procesados = 0;
    $rut_cliente = $datos_prospecto['rut_cliente'];
    $tipo_persona = $datos_prospecto['tipo_persona'];

    // Debug: Log de archivos recibidos
    error_log("=== DEBUG: Procesando archivos del checklist ===");
    error_log("Tipo de persona: " . $tipo_persona);
    
    // Obtener tipos de documentos esperados
    $checklist_types = json_decode($_POST['checklist_types'] ?? '[]', true);
    error_log("Tipos de documentos en checklist: " . print_r($checklist_types, true));
    
    // Primero, obtener el mapeo de códigos a IDs de tipo de documento
    $tipo_doc_map = [];
    $sql = "SELECT id, codigo FROM tb_inteletgroup_tipos_documento WHERE tipo_persona = ? OR tipo_persona = 'Ambos'";
    $stmt = $db->prepare($sql);
    $stmt->bind_param("s", $tipo_persona);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $tipo_doc_map[$row['codigo']] = $row['id'];
    }
    $stmt->close();
    
    // Inicializar el checklist para este prospecto
    foreach ($tipo_doc_map as $codigo => $tipo_doc_id) {
        $sql = "INSERT INTO tb_inteletgroup_documento_checklist (prospecto_id, tipo_documento_id, estado) 
                VALUES (?, ?, 'Pendiente') 
                ON DUPLICATE KEY UPDATE estado = estado";
        $stmt = $db->prepare($sql);
        $stmt->bind_param("ii", $prospecto_id, $tipo_doc_id);
        $stmt->execute();
        $stmt->close();
    }

    // Configuración de archivos
    $upload_dir = 'uploads/inteletgroup_prospectos/';
    $allowed_types = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    $max_size = 5 * 1024 * 1024; // 5MB

    // Crear directorio si no existe
    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            error_log("Error al crear directorio de uploads: $upload_dir");
            return $archivos_procesados;
        }
    }

    // Procesar archivos del checklist
    foreach ($checklist_types as $doc_type) {
        if (isset($_FILES["documento_${doc_type}"]) && isset($tipo_doc_map[$doc_type])) {
            $tipo_doc_id = $tipo_doc_map[$doc_type];
            $files = $_FILES["documento_${doc_type}"];
            
            // Si no es un array (archivo único), convertir a array
            if (!is_array($files['name'])) {
                $files = [
                    'name' => [$files['name']],
                    'type' => [$files['type']],
                    'tmp_name' => [$files['tmp_name']],
                    'error' => [$files['error']],
                    'size' => [$files['size']]
                ];
            }
            
            $file_count = count($files['name']);
            
            for ($i = 0; $i < $file_count; $i++) {
                if ($files['error'][$i] !== UPLOAD_ERR_OK) {
                    continue;
                }
                
                $file_name = $files['name'][$i];
                $file_type = $files['type'][$i];
                $file_size = $files['size'][$i];
                $file_tmp = $files['tmp_name'][$i];
                
                // Validaciones
                if (!in_array($file_type, $allowed_types)) {
                    error_log("Tipo de archivo no permitido: $file_type");
                    continue;
                }
                
                if ($file_size > $max_size) {
                    error_log("Archivo demasiado grande: $file_size bytes");
                    continue;
                }
                
                // Generar nombre único
                $extension = pathinfo($file_name, PATHINFO_EXTENSION);
                $unique_name = $rut_cliente . '_' . $doc_type . '_' . time() . '_' . $i . '.' . $extension;
                $file_path = $upload_dir . $unique_name;
                
                // Mover archivo
                if (move_uploaded_file($file_tmp, $file_path)) {
                    // Insertar en tb_inteletgroup_documentos
                    $stmt = $db->prepare("
                        INSERT INTO tb_inteletgroup_documentos (
                            prospecto_id, tipo_documento_id, usuario_id, rut_cliente, 
                            nombre_archivo, nombre_original, tipo_archivo, tamaño_archivo, ruta_archivo
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    
                    if ($stmt) {
                        $stmt->bind_param("iiissssIs",
                            $prospecto_id, $tipo_doc_id, $usuario_id, $rut_cliente, 
                            $unique_name, $file_name, $file_type, $file_size, $file_path
                        );
                        
                        if ($stmt->execute()) {
                            $documento_id = $db->insert_id;
                            
                            // Actualizar checklist
                            $stmt2 = $db->prepare("
                                UPDATE tb_inteletgroup_documento_checklist 
                                SET documento_id = ?, estado = 'Subido' 
                                WHERE prospecto_id = ? AND tipo_documento_id = ?
                            ");
                            $stmt2->bind_param("iii", $documento_id, $prospecto_id, $tipo_doc_id);
                            $stmt2->execute();
                            $stmt2->close();
                            
                            $archivos_procesados++;
                            error_log("Archivo procesado: $file_name para tipo $doc_type");
                        } else {
                            error_log("Error al insertar archivo: " . $stmt->error);
                            if (file_exists($file_path)) {
                                unlink($file_path);
                            }
                        }
                        $stmt->close();
                    }
                } else {
                    error_log("Error al mover archivo: $file_tmp -> $file_path");
                }
            }
        }
    }
    
    // Procesar documentos adicionales si existen
    if (isset($_FILES['documentos_adicionales'])) {
        $files = $_FILES['documentos_adicionales'];
        
        if (!is_array($files['name'])) {
            $files = [
                'name' => [$files['name']],
                'type' => [$files['type']],
                'tmp_name' => [$files['tmp_name']],
                'error' => [$files['error']],
                'size' => [$files['size']]
            ];
        }
        
        $file_count = count($files['name']);
        
        for ($i = 0; $i < $file_count; $i++) {
            if ($files['error'][$i] !== UPLOAD_ERR_OK || empty($files['name'][$i])) {
                continue;
            }
            
            $file_name = $files['name'][$i];
            $file_type = $files['type'][$i];
            $file_size = $files['size'][$i];
            $file_tmp = $files['tmp_name'][$i];
            
            if (!in_array($file_type, $allowed_types) || $file_size > $max_size) {
                continue;
            }
            
            $extension = pathinfo($file_name, PATHINFO_EXTENSION);
            $unique_name = $rut_cliente . '_adicional_' . time() . '_' . $i . '.' . $extension;
            $file_path = $upload_dir . $unique_name;
            
            if (move_uploaded_file($file_tmp, $file_path)) {
                $stmt = $db->prepare("
                    INSERT INTO tb_inteletgroup_documentos (
                        prospecto_id, usuario_id, rut_cliente, nombre_archivo,
                        nombre_original, tipo_archivo, tamaño_archivo, ruta_archivo
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                if ($stmt) {
                    $stmt->bind_param("iissssis",
                        $prospecto_id, $usuario_id, $rut_cliente, $unique_name,
                        $file_name, $file_type, $file_size, $file_path
                    );
                    
                    if ($stmt->execute()) {
                        $archivos_procesados++;
                    }
                    $stmt->close();
                }
            }
        }
    }

    return $archivos_procesados;
}

// -----------------------------------------------------------------
// 5. EJECUCIÓN
// -----------------------------------------------------------------
// La conexión $mysqli debe venir de 'con_db.php'
if (!isset($mysqli) || $mysqli->connect_error) {
    responder_json([
        'success' => false,
        'message' => 'Error crítico de conexión a la base de datos.'
    ], 500);
}

manejar_solicitud($mysqli);

?>