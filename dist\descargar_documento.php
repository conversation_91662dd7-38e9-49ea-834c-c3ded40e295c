<?php
// =================================================================
// Script: Descargar Documento InteletGroup
// Descripción: Sirve archivos de forma segura con autenticación
// =================================================================

// Configuración de seguridad
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Headers de seguridad
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Iniciar sesión
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    http_response_code(401);
    die('No autorizado');
}

// Verificar parámetros
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    die('Parámetro inválido');
}

$documento_id = (int)$_GET['id'];
$usuario_id = $_SESSION['usuario_id'];

// Conectar a la base de datos
require_once 'con_db.php';

if (!isset($mysqli) || $mysqli->connect_error) {
    http_response_code(500);
    die('Error de conexión a la base de datos');
}

// Consultar información del documento
$sql = "SELECT d.*, p.usuario_id as propietario_id 
        FROM tb_inteletgroup_documentos d 
        INNER JOIN tb_inteletgroup_prospectos p ON d.prospecto_id = p.id 
        WHERE d.id = ? AND p.usuario_id = ?";

$stmt = $mysqli->prepare($sql);
if (!$stmt) {
    http_response_code(500);
    die('Error en la consulta');
}

$stmt->bind_param("ii", $documento_id, $usuario_id);
$stmt->execute();

// Usar bind_result para compatibilidad con PHP más antiguo
$stmt->bind_result($doc_id, $prospecto_id, $doc_usuario_id, $rut_cliente, $nombre_archivo,
                   $nombre_original, $tipo_archivo, $tamaño_archivo, $ruta_archivo, $fecha_subida, $propietario_id);

if (!$stmt->fetch()) {
    $stmt->close();
    http_response_code(404);
    die('Documento no encontrado');
}

$documento = [
    'id' => $doc_id,
    'prospecto_id' => $prospecto_id,
    'usuario_id' => $doc_usuario_id,
    'rut_cliente' => $rut_cliente,
    'nombre_archivo' => $nombre_archivo,
    'nombre_original' => $nombre_original,
    'tipo_archivo' => $tipo_archivo,
    'tamaño_archivo' => $tamaño_archivo,
    'ruta_archivo' => $ruta_archivo,
    'fecha_subida' => $fecha_subida,
    'propietario_id' => $propietario_id
];

$stmt->close();

// Verificar que el archivo existe
$file_path = $documento['ruta_archivo'];

// Si la ruta no es absoluta, construir la ruta completa
if (!file_exists($file_path)) {
    // Intentar con ruta relativa desde el directorio actual
    $file_path = __DIR__ . '/' . $documento['ruta_archivo'];
    if (!file_exists($file_path)) {
        // Log para debug
        error_log("Archivo no encontrado. Ruta original: " . $documento['ruta_archivo'] . ", Ruta completa: " . $file_path);
        http_response_code(404);
        die('Archivo no encontrado en el servidor');
    }
}

// Obtener información del archivo
$file_size = filesize($file_path);
$file_name = $documento['nombre_original'];
$mime_type = $documento['tipo_archivo'];

// Validar tipo MIME para seguridad
$allowed_types = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
];

if (!in_array($mime_type, $allowed_types)) {
    http_response_code(403);
    die('Tipo de archivo no permitido');
}

// Determinar si es descarga o visualización
$action = $_GET['action'] ?? 'download';

// Configurar headers según la acción
if ($action === 'view') {
    // Para visualización en el navegador
    header('Content-Type: ' . $mime_type);
    header('Content-Disposition: inline; filename="' . $file_name . '"');
} else {
    // Para descarga
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $file_name . '"');
}

// Headers comunes
header('Content-Length: ' . $file_size);
header('Cache-Control: private, must-revalidate');
header('Pragma: private');
header('Expires: 0');

// Limpiar buffer de salida
if (ob_get_level()) {
    ob_end_clean();
}

// Servir el archivo
readfile($file_path);

// Registrar la descarga en logs (opcional)
error_log("Documento descargado - Usuario: {$usuario_id}, Archivo: {$file_name}, ID: {$documento_id}");

exit;
?>
